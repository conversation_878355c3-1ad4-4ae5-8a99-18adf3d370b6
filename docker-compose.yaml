services:

  # Eureka Discovery Server
  eureka:
    image: steeltoeoss/eureka-server
    container_name: eureka-server
    ports:
      - "${EUREKA_PORT:-8761}:8761"
    environment:
      - SPRING_CLOUD_NETFLIX_EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_APPLICATION_NAME=eureka-server
    networks:
      - app-net
    healthcheck:
      test: ["CMD-SHELL", "wget -qO- http://localhost:8761/actuator/health | grep UP"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s

  # Config Server (native)
  config-server:
    build:
      context: ./config-server
      dockerfile: Dockerfile
    container_name: config-server
    ports:
      - "8888:8888"
    volumes:
      - ./config-repo:/config-repo:ro
    environment:
      # native-backend instead Git/Vault
      SPRING_PROFILES_ACTIVE: native
      SERVER_PORT: 8888
      SPRING_CLOUD_EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://eureka-server:8761/eureka
    depends_on:
      eureka:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "wget -qO- http://localhost:8888/actuator/health | grep UP"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s
    networks:
      - app-net
    restart: on-failure

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: api-gateway-cts
    ports:
      - "${API_GATEWAY_PORT:-8080}:8080"
    environment:
      SPRING_APPLICATION_NAME: "api-gateway"
      SPRING_CONFIG_IMPORT: "optional:configserver:http://config-server:8888"
      SPRING_CLOUD_CONFIG_RETRY_MAX_ATTEMPTS: "6"
      SPRING_CLOUD_CONFIG_RETRY_INITIAL_INTERVAL: "2000"
      EUREKA_SERVER_URL: ${EUREKA_SERVER_URL}
    depends_on:
      config-server:
        condition: service_healthy
      eureka:
        condition: service_started
    healthcheck:
      test: ["CMD-SHELL", "wget -qO- http://localhost:8080/actuator/health | grep UP"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s
    networks:
      - app-net
    restart: on-failure

  # Postgres for user-service
  user-db:
    image: postgres:15
    container_name: cts-user-db
    restart: always
    environment:
      POSTGRES_DB: ${USER_DB_NAME}
      POSTGRES_USER: ${USER_DB_USER}
      POSTGRES_PASSWORD: ${USER_DB_PASSWORD}
    ports:
      - "${USER_DB_PORT_ON_HOST}:5432"
    networks:
      - app-net
    volumes:
      - user_pgdata:/var/lib/postgresql/data

  zookeeper:
      image: confluentinc/cp-zookeeper:7.4.4
      container_name: zookeeper
      environment:
        ZOOKEEPER_SERVER_ID: 1
        ZOOKEEPER_CLIENT_PORT: 2181
        ZOOKEEPER_TICK_TIME: 2000
      ports:
        - "2181:2181"
      healthcheck:
        test: ["CMD-SHELL", "echo srvr | nc localhost 2181 | grep 'Mode: ' || exit 1"]
        interval: 10s
        timeout: 5s
        retries: 5
        start_period: 60s
      networks:
        - app-net

  kafka:
    image: confluentinc/cp-kafka:7.4.4
    container_name: kafka
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 100
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_LOG_RETENTION_HOURS: 24
    healthcheck:
      test: [ "CMD-SHELL", "kafka-topics --bootstrap-server kafka:9092 --list" ]
      interval: 15s
      timeout: 10s
      retries: 5
    depends_on:
      zookeeper:
        condition: service_healthy
    networks:
      - app-net

  currency-gateway:
    container_name: currency-gateway
    build:
      context: ./currency-gateway
      dockerfile: Dockerfile
    env_file:
      - ./.env
    ports:
      - "8087:8080"
    depends_on:
      eureka:
          condition: service_started
      config-server:
        condition: service_started
      kafka:
        condition: service_healthy
    networks:
      - app-net

  stream-gateway:
    container_name: stream-gateway
    depends_on:
      eureka:
        condition: service_started
      config-server:
        condition: service_started
      kafka:
        condition: service_healthy
    build:
      context: ./stream-gateway
      dockerfile: Dockerfile
    env_file:
      - ./.env
    ports:
      - "8083:8080"
    networks:
      - app-net

  currency-service:
    container_name: currency-service
    build:
      context: ./currency-service
      dockerfile: Dockerfile
      args:
        MAVEN_USERNAME: ${MAVEN_USERNAME}
        MAVEN_PASSWORD: ${MAVEN_PASSWORD}
        EUREKA_SERVER_URL: ${EUREKA_SERVER_URL}
    env_file:
      - ./.env
    ports:
      - "8089:8080"
    depends_on:
      currency-db:
        condition: service_healthy
      kafka:
        condition: service_healthy
      eureka:
        condition: service_healthy
    networks:
      - app-net
  currency-db:
    image: 'mongo:8.0.6'
    container_name: currency-db
    environment:
      - 'MONGO_INITDB_DATABASE=currencyDB'
      - 'MONGO_INITDB_ROOT_PASSWORD=root'
      - 'MONGO_INITDB_ROOT_USERNAME=root'
    ports:
      - '27017:27017'
    volumes:
      - currency_db_data:/data/db
    networks:
      - app-net
    healthcheck:
      test: [ "CMD", "mongosh", "--eval", "db.runCommand({ping:1})" ]
      interval: 10s
      timeout: 5s
      retries: 5

  # User Service
  # user-service:
  #   build:
  #     context: ./user-service
  #     dockerfile: Dockerfile
  #     args:
  #       MAVEN_USERNAME: ${MAVEN_USERNAME}
  #       MAVEN_PASSWORD: ${MAVEN_PASSWORD}
  #   container_name: user-service-cts
  #   depends_on:
  #     - user-db
  #   environment:
  #     SPRING_DATASOURCE_URL: ******************************/${USER_DB_NAME}
  #     SPRING_DATASOURCE_USERNAME: ${USER_DB_USER}
  #     SPRING_DATASOURCE_PASSWORD: ${USER_DB_PASSWORD}
  #     SPRING_DATASOURCE_DRIVER_CLASS_NAME: ${DB_CLASS_NAME}
  #     EUREKA_SERVER_URL: ${EUREKA_SERVER_URL}
  #     MAVEN_USERNAME: ${MAVEN_USERNAME}
  #     MAVEN_PASSWORD: ${MAVEN_PASSWORD}
  #     JWT_PUBLIC_KEY: ${JWT_PUBLIC_KEY}
  #   ports:
  #     - "${USER_PORT:-8080}:8080"
  #     - "${USER_GRPC_PORT:-9090}:9090"
  #   networks:
  #     - app-net


networks:
  app-net:
    driver: bridge

volumes:
  user_pgdata:
  currency_db_data: